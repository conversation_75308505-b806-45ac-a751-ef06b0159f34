// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.
#pragma warning disable
using System.Numerics;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using CommunityToolkit.HighPerformance;
using NetFabric.Hyperlinq;
using Nncase.IR;
using Nncase.Targets;
using Nncase.Utilities;

namespace Nncase.CodeGen.XPU;

/// <summary>
/// StackVM function builder.
/// </summary>
internal class FunctionBuilder : IDisposable
{
    private readonly uint _id;
    private readonly SectionManager _sectionManager;
    private readonly BinaryWriter _textWriter;
    private readonly BinaryWriter _rdataWriter;
    private readonly IReadOnlyList<BinaryWriter> _threadLocalRdataWriters;
    private readonly IReadOnlyList<BinaryWriter> _blockLocalRdataWriters;

    /// <summary>
    /// NOTE sync with the k230 runtime function.
    /// </summary>
    [StructLayout(LayoutKind.Sequential)]
    private struct MemoryRange
    {
        public uint Start;
        public uint Size;
    }

    /// <summary>
    /// NOTE sync with the k230 runtime function.
    /// </summary>
    [StructLayout(LayoutKind.Sequential)]
    private unsafe struct DescHeader
    {
        /// <summary>
        /// input pool size.
        /// </summary>
        public uint InputPoolSize;

        /// <summary>
        /// output pool size.
        /// </summary>
        public uint OutputPoolSize;

        /// <summary>
        /// input numbers.
        /// </summary>
        public uint Inputs;

        /// <summary>
        /// output numbers.
        /// </summary>
        public uint Outputs;

        /// <summary>
        /// Data usage.
        /// </summary>
        public uint DataUsage;

        /// <summary>
        /// Output align.
        /// </summary>
        public uint OutputAlign;

        /// <summary>
        /// Function Name.
        /// </summary>
        public fixed byte Name[64];
    }

    public FunctionBuilder(uint id, BinaryWriter rdataWriter, IReadOnlyList<BinaryWriter> threadLocalRdataWriters, IReadOnlyList<BinaryWriter> blockLocalRdataWriters, Targets.NTTTargetOptions targetOptions)
    {
        _id = id;
        _sectionManager = new();
        _textWriter = _sectionManager.GetWriter(WellknownSectionNames.Text);
        _rdataWriter = rdataWriter;
        _threadLocalRdataWriters = threadLocalRdataWriters;
        _blockLocalRdataWriters = blockLocalRdataWriters;
        TargetOptions = targetOptions;
    }

    public NTTTargetOptions TargetOptions { get; }

    public unsafe ILinkableFunction Build(BaseFunction baseFunc)
    {
        if (baseFunc is TIR.PrimFunction primFunc)
        {
            if (!primFunc.Name.Contains("device_func", StringComparison.Ordinal))
            {
                // 1. write the rdata
                ulong rdataPoolSize = ulong.MinValue;
                foreach (var (@const, range) in primFunc.SchedResult.Rdatas)
                {
                    var tensor = ((TensorConst)@const).Value;
                    var size = range.Max - range.Min;
                    rdataPoolSize = System.Math.Max(range.Max, rdataPoolSize);
                    if ((ulong)tensor.Length * (ulong)tensor.ElementType.SizeInBytes != size)
                    {
                        throw new InvalidDataException("The Buffer Size Not Equal!");
                    }

                    _rdataWriter.Position(checked((long)range.Min));
                    tensor.Serialize(_rdataWriter.BaseStream);
                }

                // 2. write the local rdata
                ulong threadLocalRdataPoolSize = ulong.MinValue;
                foreach (var (@const, range) in primFunc.SchedResult.ThreadLocalRdatas)
                {
                    var tensor = ((TensorConst)@const).Value;
                    var distributedType = (DistributedType)@const.CheckedType;
                    var size = range.Max - range.Min;
                    threadLocalRdataPoolSize = System.Math.Max(range.Max, threadLocalRdataPoolSize);
                    var dividedDims = DistributedUtility.GetDividedTensorType(distributedType).Shape.ToValueArray();
                    var localStrides = TensorUtilities.GetDefaultStrides(dividedDims);
                    for (int i = 0; i < _threadLocalRdataWriters.Count; i++)
                    {
                        var localRdataWriter = _threadLocalRdataWriters[i];
                        var shardIndex = DistributedUtility.GetUnraveledIndex(i, TargetOptions.Hierarchies[0]);
                        (var localOffset, var localShape) = DistributedUtility.GetLocalOffsetAndShape(distributedType, shardIndex);
                        var linearOffset = TensorUtilities.GetLinearOffset(tensor.Strides, localOffset);

                        if ((ulong)TensorUtilities.GetProduct(localShape) * (ulong)tensor.ElementType.SizeInBytes > size)
                        {
                            throw new InvalidDataException("The Buffer Size Not Equal!");
                        }

                        localRdataWriter.Position(checked((long)range.Min));
                        tensor.Serialize(localRdataWriter.BaseStream, linearOffset, localShape, localStrides);
                    }
                }

                // 3. write the block local rdata
                ulong blockLocalRdataPoolSize = ulong.MinValue;
                foreach (var (@const, range) in primFunc.SchedResult.BlockLocalRdatas)
                {
                    var tensor = ((TensorConst)@const).Value;
                    var distributedType = (DistributedType)@const.CheckedType;
                    var size = range.Max - range.Min;
                    blockLocalRdataPoolSize = System.Math.Max(range.Max, blockLocalRdataPoolSize);
                    var dividedDims = DistributedUtility.GetDividedTensorType(distributedType).Shape.ToValueArray();
                    var localStrides = TensorUtilities.GetDefaultStrides(dividedDims);
                    for (int i = 0; i < _blockLocalRdataWriters.Count; i++)
                    {
                        var localRdataWriter = _blockLocalRdataWriters[i];
                        var shardIndex = DistributedUtility.GetUnraveledIndex(i, TargetOptions.Hierarchies[0][..^1]).Concat([0]).ToArray();
                        (var localOffset, var localShape) = DistributedUtility.GetLocalOffsetAndShape(distributedType, shardIndex);
                        var linearOffset = TensorUtilities.GetLinearOffset(tensor.Strides, localOffset);

                        if ((ulong)TensorUtilities.GetProduct(localShape) * (ulong)tensor.ElementType.SizeInBytes > size)
                        {
                            throw new InvalidDataException("The Buffer Size Not Equal!");
                        }

                        localRdataWriter.Position(checked((long)range.Min));
                        tensor.Serialize(localRdataWriter.BaseStream, linearOffset, localShape, localStrides);
                    }
                }

                // 4. convert func to csource
                var deviceDataUsage = Utilities.MathUtility.AlignUp(primFunc.SchedResult.DataUsage, XPUModuleBuilder.DeviceDataAlign);
                var visitor = new KernelCSourceConvertVisitor(XPUModuleBuilder.DeviceDataAlign, deviceDataUsage, 0, threadLocalRdataPoolSize, blockLocalRdataPoolSize, TargetOptions);
                visitor.Visit(primFunc);
                var functionCSource = visitor.GetCSource();

                // 5. write the desc
                var descSection = new SectionManager();
                using (var descWriter = descSection.GetWriter(".desc"))
                {
                    DescHeader header = new() { InputPoolSize = 0, OutputPoolSize = 0, Inputs = 0, Outputs = 0, DataUsage = (uint)deviceDataUsage };
                    long headerStart = descWriter.Position();
                    descWriter.Skip((ulong)sizeof(DescHeader));

                    header.Inputs = (uint)primFunc.Parameters.Length;

                    header.OutputAlign = (uint)primFunc.SchedResult.OutputAlign;
                    header.OutputPoolSize = (uint)primFunc.SchedResult.OutputUsage;

                    var wrapperName = primFunc.Name + "_wrapper";
                    for (int i = 0; i < wrapperName.Length; i++)
                    {
                        header.Name[i] = (byte)wrapperName[i];
                    }

                    descWriter.Position(headerStart);
                    descWriter.Write(ref header);
                }

                return new LinkableKernelFunction(_id, primFunc, functionCSource, _sectionManager.GetContent(WellknownSectionNames.Text), descSection.GetContent(".desc"));
            }
            else
            {
                var visitor = new DeviceCSourceConvertVisitor(TargetOptions);
                visitor.Visit(primFunc);
                var header = visitor.GetHeader();
                return new LinkableDeviceFunction(_id, primFunc, header, _sectionManager.GetContent(WellknownSectionNames.Text)!);
            }
        }
        else if (baseFunc is Fusion fusion)
        {
            var visitor = new CodeGen.NTT.LambdaCSourceConvertVisitor();
            visitor.Visit(fusion);
            var header = visitor.GetHeader();
            return new LinkableLambdaFunction(_id, fusion, header, _sectionManager.GetContent(WellknownSectionNames.Text)!);
        }

        throw new NotSupportedException($"the {baseFunc.GetType()} {baseFunc.Name} is notsupport for codegen!");
    }

    public void Dispose()
    {
    }

    private void WriteRdataSeg<T>(Tensor tensor, ValueRange<ulong> range)
        where T : unmanaged, INumber<T>
    {
        var buffer = tensor.ToArray<T>().AsSpan();
        var dt = tensor.ElementType;
        var size = range.Max - range.Min;
        if ((ulong)buffer.Length * (ulong)dt.SizeInBytes != size)
        {
            throw new InvalidDataException("The Buffer Szie Not Equal!");
        }

        var chunck = 1024 * 1024 * 1024L / dt.SizeInBytes;
        int written = 0;
        long length = buffer.Length;
        _rdataWriter.Position((long)range.Min);
        while (length > 0)
        {
            var sizeToWrite = (int)System.Math.Min(length, chunck);
            _rdataWriter.Write(MemoryMarshal.Cast<T, byte>(buffer.Slice(written, sizeToWrite)));
            written += sizeToWrite;
            length -= sizeToWrite;
        }
    }

    private void WriteRdataSeg<T, TVector>(Tensor tensor, ValueRange<ulong> range)
        where T : unmanaged, INumber<T>
        where TVector : unmanaged, IEquatable<TVector>
    {
        var buffer = tensor.ToArray<TVector>().AsSpan();
        var dt = tensor.ElementType;
        var size = range.Max - range.Min;
        if ((ulong)buffer.Length * (ulong)dt.SizeInBytes != size)
        {
            throw new InvalidDataException("The Buffer Size Not Equal!");
        }

        var chunck = 1024 * 1024 * 1024L / dt.SizeInBytes;
        int written = 0;
        long length = buffer.Length;
        _rdataWriter.Position((long)range.Min);
        while (length > 0)
        {
            var sizeToWrite = (int)System.Math.Min(length, chunck);
            _rdataWriter.Write(MemoryMarshal.Cast<TVector, byte>(buffer.Slice(written, sizeToWrite)));
            written += sizeToWrite;
            length -= sizeToWrite;
        }
    }

    private void WriteVectorRdata<T>(Tensor tensor, ValueRange<ulong> range, IRArray<int> lanes)
        where T : unmanaged, INumber<T>, IEquatable<T>
    {
        switch (lanes)
        {
            case var _ when lanes.ToArray().SequenceEqual([4]):
                WriteRdataSeg<T, Vector4<T>>(tensor, range);
                break;
            case var _ when lanes.ToArray().SequenceEqual([4, 4]):
                WriteRdataSeg<T, Vector4x4<T>>(tensor, range);
                break;
            case var _ when lanes.ToArray().SequenceEqual([8]):
                WriteRdataSeg<T, Vector8<T>>(tensor, range);
                break;
            case var _ when lanes.ToArray().SequenceEqual([8, 8]):
                WriteRdataSeg<T, Vector8x8<T>>(tensor, range);
                break;
            case var _ when lanes.ToArray().SequenceEqual([16]):
                WriteRdataSeg<T, Vector16<T>>(tensor, range);
                break;
            case var _ when lanes.ToArray().SequenceEqual([16, 16]):
                WriteRdataSeg<T, Vector16x16<T>>(tensor, range);
                break;
            case var _ when lanes.ToArray().SequenceEqual([32]):
                WriteRdataSeg<T, Vector32<T>>(tensor, range);
                break;
            case var _ when lanes.ToArray().SequenceEqual([32, 16]):
                WriteRdataSeg<T, Vector32x16<T>>(tensor, range);
                break;
            case var _ when lanes.ToArray().SequenceEqual([32, 32]):
                WriteRdataSeg<T, Vector32x32<T>>(tensor, range);
                break;
            case var _ when lanes.ToArray().SequenceEqual([32, 64]):
                WriteRdataSeg<T, Vector32x64<T>>(tensor, range);
                break;
            case var _ when lanes.ToArray().SequenceEqual([64]):
                WriteRdataSeg<T, Vector64<T>>(tensor, range);
                break;
            case var _ when lanes.ToArray().SequenceEqual([128]):
                WriteRdataSeg<T, Vector128<T>>(tensor, range);
                break;
            case var _ when lanes.ToArray().SequenceEqual([32, 128]):
                WriteRdataSeg<T, Vector32x128<T>>(tensor, range);
                break;
            case var _ when lanes.ToArray().SequenceEqual([64, 32]):
                WriteRdataSeg<T, Vector64x32<T>>(tensor, range);
                break;
            case var _ when lanes.ToArray().SequenceEqual([64, 64]):
                WriteRdataSeg<T, Vector64x64<T>>(tensor, range);
                break;
            case var _ when lanes.ToArray().SequenceEqual([64, 128]):
                WriteRdataSeg<T, Vector64x128<T>>(tensor, range);
                break;
            case var _ when lanes.ToArray().SequenceEqual([128, 64]):
                WriteRdataSeg<T, Vector128x64<T>>(tensor, range);
                break;
            default:
                throw new NotSupportedException($"Not supported onnx constant vector type");
        }
    }
}
