﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System;
using System.Collections.Generic;
using System.CommandLine;
using System.CommandLine.Invocation;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Nncase.CodeGen;
using Nncase.CodeGen.XPU;
using Nncase.IR;
using Nncase.Passes;
using Nncase.Passes.Rules.Neutral;
using Nncase.Passes.Rules.ShapeBucket;
using Nncase.Passes.Transforms;
using Nncase.Quantization;

namespace Nncase.Targets;

/// <summary>
/// Target for XPU.
/// </summary>
public sealed class XPUTarget : Target
{
    /// <summary>
    /// Gets kind.
    /// </summary>
    public const string Kind = "xpu";

    private readonly XPUModuleCompiler _xpuModuleCompiler = new();

    public XPUTarget()
    {
        ModuleCompilers = [_xpuModuleCompiler];
    }

    public enum Topology : int
    {
        Chip = 0,
        Die,
        Block,
        Thread,
    }

    public static int Rank => 1;

    public static int Lane => 128;

    public override string Name => Kind;

    public override IReadOnlyList<IModuleCompiler> ModuleCompilers { get; }

    public override (System.CommandLine.Command Command, Func<InvocationContext, System.CommandLine.Command, ITargetOptions> Parser) RegisterCommandAndParser()
    {
        var cmd = new NTTTargetOptionsCommand(Kind);

        ITargetOptions ParseTargetCompileOptions(InvocationContext context, Command command)
        {
            var binder = new NTTTargetOptionsBinder(cmd);
            return binder.GetBoundValue(context);
        }

        return (cmd, ParseTargetCompileOptions);
    }

    public override void RegisterAffineSelectionPass(IPassManager passManager, CompileOptions options)
    {
        // TODO: reopen when tiling supports non-uniform split
        passManager.Add<XPUAffineSelectionPass>(options, XPUTarget.Kind);
    }

    public override void RegisterAutoVectorizeRules(IRulesAddable pass, CompileOptions options)
    {
        if (options.TargetOptions is NTTTargetOptions cpuOptions && cpuOptions.Vectorize)
        {
            var maskVectorStyle = _xpuModuleCompiler.MaskVectorStyle;

            // todo config it in the target options.
            var rank = Rank;
            var lane = Lane;
            pass.Add<Passes.Rules.NTT.VectorizeBinaryPropagation>();
            pass.Add<Passes.Rules.NTT.VectorizeComparePropagation>(maskVectorStyle);
            pass.Add<Passes.Rules.NTT.VectorizeConcatPropagation>();
            pass.Add<Passes.Rules.NTT.VectorizeConv2D>(rank, lane);
            pass.Add<Passes.Rules.NTT.VectorizeExpandPropagation>();
            pass.Add<Passes.Rules.NTT.VectorizeGatherPropagation>();
            pass.Add<Passes.Rules.NTT.VectorizeLayerNorm>(rank, lane);
            pass.Add<Passes.Rules.XPU.VectorizeMatMul>(2, lane);
            pass.Add<Passes.Rules.NTT.VectorizePadPropagation>();
            pass.Add<Passes.Rules.NTT.VectorizeReducePropagation>();
            pass.Add<Passes.Rules.NTT.VectorizeReshapePropagation>();
            pass.Add<Passes.Rules.NTT.VectorizeResizeImagePropagation>();

            // pass.Add<Passes.Rules.NTT.VectorizeScatterND>(rank, lane);
            pass.Add<Passes.Rules.NTT.VectorizeSlicePropagation>();

            // pass.Add<Passes.Rules.NTT.VectorizeSwish>(rank, lane);
            pass.Add<Passes.Rules.NTT.VectorizeTransposePropagation>();
            pass.Add<Passes.Rules.NTT.VectorizeUnaryPropagation>();
            pass.Add<Passes.Rules.NTT.VectorizeUnsqueezePropagation>();
            pass.Add<Passes.Rules.NTT.VectorizeWherePropagation>(maskVectorStyle);

            pass.Add<Passes.Rules.NTT.ConcatDevectorizePropagation>();
            pass.Add<Passes.Rules.NTT.BinaryDevectorizeLhsPropagation>();
            pass.Add<Passes.Rules.NTT.BinaryDevectorizeRhsPropagation>();
            pass.Add<Passes.Rules.NTT.VectorizedMatMulDevectorizePropagation>();
            pass.Add<Passes.Rules.NTT.ReshapeDevectorizePropagation>();
            pass.Add<Passes.Rules.NTT.SliceDevectorizePropagation>();

            pass.Add<Passes.Rules.NTT.SwishDevectorizePropagation>();
            pass.Add<Passes.Rules.NTT.TransposeDevectorizePropagation>();
            pass.Add<Passes.Rules.NTT.UnaryDevectorizePropagation>();

            pass.Add<Passes.Rules.NTT.VectorizeCastPropagation>();

            pass.Add<Passes.Rules.Neutral.FoldConstCall>();
            pass.Add<Passes.Rules.NTT.FoldVectorizeDevectorize>();
            pass.Add<Passes.Rules.NTT.FoldVectorizeConcatDevectorize>();
            pass.Add<Passes.Rules.Neutral.FoldTwoReshapes>();
            pass.Add<Passes.Rules.Neutral.FoldTwoTransposes>();
            pass.Add<Passes.Rules.Neutral.FoldTwoNopCasts>();
        }
    }

    public override void RegisterTIRSelectionPass(IPassManager passManager, CompileOptions options)
    {
        passManager.Add<NTTTIRSelectionPass>(options, XPUTarget.Kind);
    }

    /// <inheritdoc/>
    public override void RegisterTargetDependentPass(IPassManager passManager, CompileOptions options)
    {
        passManager.Add<XPUModuleConvertPass>(XPUTarget.Kind);
        passManager.Add<DataflowPass>().Configure(p =>
        {
            p.Add<Passes.Rules.FoldPrePostReshapeSoftmax>();
        });
    }

    /// <inheritdoc/>
    public override void RegisterQuantizePass(IPassManager passManager, CompileOptions options)
    {
        if (options.QuantizeOptions.ModelQuantMode == ModelQuantMode.UsePTQ)
        {
            passManager.AddWithName<DataflowPass>("Quantizer").Configure(p =>
            {
                p.Add<Passes.Rules.Lower.QuantizerMatmul>();
                p.Add<Passes.Rules.Neutral.ScalarConstToTensor>();
            });
        }
    }

    /// <inheritdoc/>
    public override void RegisterPostQuantizePass(IPassManager passManager, CompileOptions options)
    {
        passManager.Add<Passes.Distributed.CustomOpSubstitutePass>(options);
        passManager.Add<DataflowPass>().Configure(p =>
        {
            p.Add<Passes.Rules.XPU.VectorizeCustomMatMul>();
            p.Add<Passes.Rules.Neutral.FoldConstCall>();
        });

        passManager.Add<DataflowPass>().Configure(p =>
        {
            p.Add<Passes.Rules.Neutral.CombineGLMTuple>();
        });
    }

    public override void RegisterPostAutoVectorizePass(IPassManager passManager, CompileOptions options)
    {
        // need refactor tiling.
        passManager.Add<DataflowPass>().Configure(p =>
            {
                p.Add<Passes.Rules.Conv2dToMatMul>();

                // p.Add<Passes.Rules.NTT.FoldCastPostOps>();
                // p.Add<Passes.Rules.NTT.FoldBinaryPostOps>();
            });
    }
}
