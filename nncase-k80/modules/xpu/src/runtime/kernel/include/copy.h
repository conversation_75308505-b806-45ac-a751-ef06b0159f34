/* Copyright 2019-2021 Canaan Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#pragma once
#ifdef SYS_MODE
#include "isa.h"
#include <nncase/ntt/apply.h>
#include <nncase/ntt/utility.h>

namespace nncase::ntt
{
namespace copy_detail
{
    template <bool load, typename TA, typename TB>
    struct copy_impl_matmul;
    template <bool load, Tensor TA, Tensor TB>
    struct copy_impl_matmul<load, TA, TB>
    {
        constexpr void operator()(const TA &input, TB &output)
        {
            auto input_shape = input.shape();

            using TInElem = typename TA::element_type;
            using TOutElem = typename std::decay_t<TB>::element_type;
            constexpr auto a_shape = typename TInElem::shape_type {};
            constexpr auto b_shape = typename TOutElem::shape_type {};
            constexpr auto a_strides = typename TInElem::strides_type {};
            constexpr auto b_strides = typename TOutElem::strides_type {};
            constexpr auto in_type = to_tdma_type<typename TInElem::element_type>();

            apply(input_shape, [&](auto index)
                {
                    if constexpr (load)
                    {
                        tdma_vhm2dm<tdma_mode::tile, mem_format::ndhwc, mem_format::ncdwhcx, in_type>(a_shape, a_shape, reinterpret_cast<const char *>(&input(index)), reinterpret_cast<char *>(&output(index)));
                    }
                    else
                    {
                        tdma_dm2vhm<tdma_mode::tile, mem_format::ncdwhcx, mem_format::ndhwc, in_type>(b_shape, b_shape, reinterpret_cast<const char *>(&input(index)), reinterpret_cast<char *>(&output(index)));
                    } });
        }
    };

    template <bool load, typename TA, typename TB>
    struct copy_impl_vector;
    template <bool load, Tensor TA, Tensor TB>
    struct copy_impl_vector<load, TA, TB>
    {
        template <class TShape, class TStrides>
        constexpr void do_copy_impl(const TA &input, TB &output, const TShape &vhm_shape, const TStrides &vhm_strides)
        {
            constexpr auto rank = TShape::rank();
            using TInElem = typename TA::element_type;
            constexpr auto in_type = to_tdma_type<TInElem>();

            if (((uintptr_t)input.elements().data()) % 128 == 0 && ((uintptr_t)output.elements().data()) % 128 == 0 && vhm_shape[rank - 1] * sizeof(TInElem) % 128 == 0)
            {
                ntt::dynamic_shape_t<2> copy_shape;
                ntt::dynamic_shape_t<2> copy_stride;
                if constexpr (rank == 1)
                {
                    if constexpr (Scalar<TInElem>)
                    {
                        copy_shape = ntt::make_shape(1, vhm_shape[rank - 1]);
                    }
                    else
                    {
                        constexpr auto ele_shape = typename TInElem::shape_type {};
                        copy_shape = ntt::make_shape(1, vhm_shape[rank - 1] * ele_shape.length());
                    }
                    copy_stride = copy_shape;

                    if constexpr (load)
                    {
                        tdma_vhm2dm<tdma_mode::tile, mem_format::ndhwc, mem_format::ndhwc, in_type>(copy_shape, copy_stride, reinterpret_cast<const char *>(input.elements().data()), reinterpret_cast<char *>(output.elements().data()));
                    }
                    else
                    {
                        tdma_dm2vhm<tdma_mode::tile, mem_format::ndhwc, mem_format::ndhwc, in_type>(copy_shape, copy_stride, reinterpret_cast<const char *>(input.elements().data()), reinterpret_cast<char *>(output.elements().data()));
                    }
                }
                else
                {
                    auto apply_shape = vhm_shape.template slice<rank - 2>();
                    auto apply_in_stride = input.strides().template slice<rank - 2>();
                    auto apply_out_stride = output.strides().template slice<rank - 2>();

                    if constexpr (Scalar<TInElem>)
                    {
                        copy_shape = ntt::make_shape(vhm_shape[rank - 2], vhm_shape[rank - 1]);
                        copy_stride = ntt::make_shape(vhm_shape[rank - 2], vhm_strides[rank - 2]);
                    }
                    else
                    {
                        constexpr auto ele_shape = typename TInElem::shape_type {};
                        copy_shape = ntt::make_shape(vhm_shape[rank - 2], vhm_shape[rank - 1] * ele_shape.length());
                        copy_stride = ntt::make_shape(vhm_shape[rank - 2], vhm_strides[rank - 2] * ele_shape.length());
                    }

                    apply(apply_shape, [&](auto index)
                        {
                            auto in_begin = input.elements().data() + linear_offset(index, apply_in_stride);
                            auto out_begin = output.elements().data() + linear_offset(index, apply_out_stride);
                            if (load)
                            {
                                tdma_vhm2dm<tdma_mode::tile, mem_format::ndhwc, mem_format::ndhwc, in_type>(copy_shape, copy_stride, reinterpret_cast<const char *>(in_begin), reinterpret_cast<char *>(out_begin));
                            }
                            else
                            {
                                tdma_dm2vhm<tdma_mode::tile, mem_format::ndhwc, mem_format::ndhwc, in_type>(copy_shape, copy_stride, reinterpret_cast<const char *>(in_begin), reinterpret_cast<char *>(out_begin));
                            } });
                }
            }
            else
            {
                ntt::unary<ops::copy>(input, output);
            }
        }

        constexpr void operator()(const TA &input, TB &output)
        {
            if constexpr (load)
            {
                do_copy_impl(input, output, input.shape(), input.strides());
            }
            else
            {
                do_copy_impl(input, output, output.shape(), output.strides());
            }
        }
    };
} // namespace copy_detail

template <bool load, bool interleave = true, class TA, class TB>
void tensor_copy_tdma(const TA &input, TB &&output) noexcept
{
    if constexpr (interleave)
    {
        copy_detail::copy_impl_matmul<load, TA, std::decay_t<TB>> impl;
        impl(input, output);
    }
    else
    {
        copy_detail::copy_impl_vector<load, TA, std::decay_t<TB>> impl;
        impl(input, output);
    }
}
} // namespace nncase::ntt
#endif