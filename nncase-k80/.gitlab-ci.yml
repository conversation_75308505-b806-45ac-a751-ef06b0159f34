variables:
  NNCASE_BRANCH: dev/3.0
  NNCASE_COMMIT: dev/3.0
  SOCKS_PROXY: socks5://**********:1080
  SDK_DOCKER_IMAGE: sw-docker.a-bug.org:5000/sdk/duca:v0.3.0

stages:
- build
- test

linux build python job:
  except:
    - schedules
  tags:
    - nncase-wheel
    - linux
  stage: build
  script:
    - source ~/.bashrc
    - export DOTNET_NUGET_SIGNATURE_VERIFICATION=false
    - export DOTNET_ROOT=/compiler/gitlab-runner/dotnet/dotnet-sdk-8.0.111
    - export PATH=$PATH:/compiler/gitlab-runner/dotnet/dotnet-sdk-8.0.111
    - pip3 config set global.index-url https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple
    - pip3 install --upgrade pip
    - pip3 --default-timeout=1000 install "conan==2.6.0" cibuildwheel
    - rm -rf ../nncase
    # - cp -r /compiler/gitlab-runner/nncase ..
    - git clone https://github.com/kendryte/nncase.git ../nncase -c http.proxy=$SOCKS_PROXY -c https.proxy=$SOCKS_PROXY
    - cd ../nncase
    - git fetch origin $NNCASE_BRANCH
    - git reset --hard origin/$NNCASE_BRANCH
    - git checkout $NNCASE_COMMIT
    - until dotnet restore -r linux-x64; do :; done
    - dotnet publish src/Nncase.Compiler -c Release --no-restore --sc false -r linux-x64
    - cd -
    - until dotnet restore -r linux-x64; do :; done
    - dotnet publish modules/Nncase.Modules.XPU -c Release --no-restore --sc false -r linux-x64
    - mv ../nncase .
    - /home/<USER>/.local/bin/cibuildwheel --output-dir k80-wheelhouse-linux --platform linux
  artifacts:
    paths:
      - k80-wheelhouse-linux
  timeout: 1h

linux runtime job:
  except:
    - schedules
  tags:
    - nncase_v3_benchmark
  stage: build
  image:
    name: compilerteamer/ubuntu24.04:v0.6
    pull_policy: if-not-present
  script:
    # 0. setup
    - source ~/python310_venv/bin/activate
    - conan remote add sunnycase https://conan.sunnycase.moe --index 0
    - x86_64_gcc=/compiler/gitlab-runner/toolchain/x86_64_gcc_14.2.0
    - update-alternatives --install /usr/bin/gcc gcc ${x86_64_gcc}/bin/gcc-14.2.0 14
    - update-alternatives --install /usr/bin/g++ g++ ${x86_64_gcc}/bin/g++-14.2.0 14
    - k80=`pwd`
    - wget http://sw-file.a-bug.org/k80/common/sdk/duca/release/v0.3.1/duca-v0.3.1-20250617-184235-90de4e0.tar.gz -O duca.tgz
    - mkdir -p duca
    - tar xzf duca.tgz -C duca
    - runtime=${k80}/xpu_runtime
    - build=runtime/build/Release

    # 1. nncase
    - rm -rf ../nncase
    - git clone https://github.com/kendryte/nncase.git ../nncase -c http.proxy=$SOCKS_PROXY -c https.proxy=$SOCKS_PROXY
    - cd ../nncase
    - git reset --hard origin/$NNCASE_BRANCH
    - git checkout $NNCASE_COMMIT
    - rm -rf CMakeUserPresets.json runtime
    - conan install . -of runtime --build=missing -s build_type=Release -pr:a=toolchains/x86_64-linux.profile.jinja -o "&:runtime=True" -o "&:python=True" -o "&:tests=False" -o "&:k80_runtime=True"
    - |
      cmake -DCMAKE_BUILD_TYPE=Release \
            -DBUILDING_RUNTIME=1 \
            -DENABLE_K80_RUNTIME=1 \
            -DENABLE_OPENMP=0 \
            -DBUILD_PYTHON_BINDING=1 \
            -DCMAKE_TOOLCHAIN_FILE=generators/conan_toolchain.cmake \
            -DDEFAULT_BUILTIN_RUNTIMES=0 \
            -DENABLE_OP_PROFILE=0 \
            -S . \
            -B ${build}
    - cmake --build ${build} -j32
    - cmake --install ${build} --prefix=${runtime}
    - cd -

    # 2. nncase-k80
    - rm -rf CMakeUserPresets.json runtime
    - conan install . -of runtime --build=missing -s build_type=Release -pr:a=../nncase/toolchains/x86_64-linux.profile.jinja -o "&:runtime=True" -o "&:python=True" -o "&:tests=False" -o "&:k80_runtime=True"
    - |
      cmake -DCMAKE_BUILD_TYPE=Release \
            -DBUILDING_RUNTIME=1 \
            -DENABLE_K80_RUNTIME=1 \
            -DENABLE_OPENMP=0 \
            -DCMAKE_TOOLCHAIN_FILE=generators/conan_toolchain.cmake \
            -DDEFAULT_BUILTIN_RUNTIMES=0 \
            -DBUILD_PYTHON_BINDING=1 \
            -DENABLE_OP_PROFILE=0 \
            -DENABLE_XPU_RUNTIME=1 \
            -DDISABLE_RVV=0 \
            -Dnncaseruntime_DIR=${runtime}/lib/cmake/nncaseruntime/ \
            -DDUCA_PATH=${k80}/duca/output/qemu/duca \
            -S . \
            -B ${build}
    - cmake --build ${build} -j32
    - cmake --install ${build} --prefix=${runtime}
    - mkdir -p k80-wheelhouse-runtime-linux
    - cp ${build}/modules/xpu/examples/xpu_test/xpu_test.elf k80-wheelhouse-runtime-linux

    # 3. runtime python wheel
    - pip install GitPython wheel
    - export NNCASE_RUNTIME_LIB=${runtime}
    - cd python
    - python3 setup.py bdist_wheel
    - cd -
    - cp python/dist/*.whl k80-wheelhouse-runtime-linux
  artifacts:
    paths:
      - k80-wheelhouse-runtime-linux
  timeout: 1h

linux test job:
  except:
    - schedules
  tags:
    - nncase
  stage: test
  image:
    name: compilerteamer/manylinux_2_28_x86_64:nncase_v2
    pull_policy: if-not-present
  script:
    - echo 'export PATH="/usr/local/bin:$PATH"' >> ~/.bash_profile
    - source ~/.bashrc
    - source ~/activate_py311.sh
    - pip --default-timeout=1000 install --index-url https://mirrors.aliyun.com/pypi/simple/ "conan==2.6.0"
    - conan remote add sunnycase https://conan.sunnycase.moe --index 0
    - export CC="gcc-14"
    - export CXX="g++-14"

    # 1. compile nncase native & export the nncase native lib
    - rm -rf ../nncase
    # - git clone https://github.com/kendryte/nncase.git ../nncase -c http.proxy=$SOCKS_PROXY -c https.proxy=$SOCKS_PROXY
    - cp -r /compiler/gitlab-runner/nncase ..
    - cd ../nncase
    - git fetch origin $NNCASE_BRANCH
    - git reset --hard origin/$NNCASE_BRANCH
    - git checkout $NNCASE_COMMIT
    - nncase_install=`pwd`/install
    - rm -rf CMakeUserPresets.json build ${nncase_install}
    - conan install . --build=missing -s build_type=Release -pr:a=toolchains/x86_64-linux.profile.jinja -o "&:runtime=False" -o "&:python=True" -o "&:tests=False"
    - cmake --preset conan-release
    - cmake --build build/Release --config Release -j8
    - cmake --install build/Release --prefix=${nncase_install}
    - cd -

    # 2. compile nncase-k80 native and exports native libs
    - rm -rf CMakeUserPresets.json build
    - conan install . --build=missing -s build_type=Release -pr:a=../nncase/toolchains/x86_64-linux.profile.jinja -o "&:nncase_dir=${nncase_install}/lib/cmake/nncase" -o "&:runtime=False" -o "&:python=True" -o "&:tests=False"
    - cmake --preset conan-release
    - cmake --build build/Release --config Release -j8
    - cmake --install build/Release --prefix=${nncase_install}

    # 3. dotnet test
    - export LD_LIBRARY_PATH="${nncase_install}/lib:${LD_LIBRARY_PATH}"
    - export DYLD_LIBRARY_PATH="${nncase_install}/lib:${DYLD_LIBRARY_PATH}"
    - export NNCASE_TILING_MAX_SOLUTIONS=1
    - until dotnet restore; do :; done
    - dotnet build -c Release
    - dotnet test -c Release --filter=UnitTestXPUTargetTiling --verbosity normal --blame
    - dotnet test -c Release --filter=UnitTestXPUKernels --verbosity normal --blame
  timeout: 3h

benchmark ntt job:
  allow_failure: false
  rules:
    - if: '$CI_PIPELINE_SOURCE == "schedule" && $SCHEDULE_TYPE == "k80_benchmark_ntt"'
  tags:
    - nncase_v3_benchmark
  stage: test
  image:
    name: compilerteamer/ubuntu24.04:v0.6
    pull_policy: if-not-present
  script:
    - source ~/python311_venv/bin/activate
    - pip --default-timeout=1000 install confluence.md==0.2.3
    # - NNCASE_BRANCH=feature/ntt_benchmark_roofline_6
    # - NNCASE_COMMIT=feature/ntt_benchmark_roofline_6
    - K510_DIR=`pwd`
    - build=build
    - build_type=Release
    - cd ..

    # 1. nncase
    - rm -rf nncase
    - git clone https://github.com/kendryte/nncase.git -c http.proxy=$SOCKS_PROXY -c https.proxy=$SOCKS_PROXY
    - cd nncase
    - git reset --hard origin/$NNCASE_BRANCH
    - git checkout $NNCASE_COMMIT

    # 2. x86_64 build
    - x86_64_gcc=/compiler/gitlab-runner/toolchain/x86_64_gcc_14.2.0
    - update-alternatives --install /usr/bin/gcc gcc ${x86_64_gcc}/bin/gcc-14.2.0 14
    - update-alternatives --install /usr/bin/g++ g++ ${x86_64_gcc}/bin/g++-14.2.0 14
    - conan install . --build=missing -s build_type=${build_type} -pr:a=toolchains/x86_64-linux.profile.jinja -o "&:runtime=True" -o "&:python=False" -o "&:BUILD_PYTHON_BINDING=False"
    - cmake --preset conan-runtime-release
    - cmake --build ${build}/${build_type} --config ${build_type}
    - cmake --install ${build}/${build_type} --prefix install
    - mv ${build}/${build_type} x86_64_build
    - rm -rf ${build}

    # 3. riscv64 build
    - export RISCV_ROOT_PATH="/compiler/gitlab-runner/toolchain/riscv64-unknown-linux_gnu_14.2.0"
    - export CC=${RISCV_ROOT_PATH}/bin/riscv64-unknown-linux-gnu-gcc
    - export CXX=${RISCV_ROOT_PATH}/bin/riscv64-unknown-linux-gnu-g++
    - conan install . --build=missing -s build_type=${build_type} -pr:h=toolchains/riscv64-linux.profile.jinja -pr:b=toolchains/x86_64-linux.profile.jinja -o "&:runtime=True" -o "&:python=False" -o "&:BUILD_PYTHON_BINDING=False" -o "&:k230_runtime=True"
    - cmake --preset conan-runtime-release
    - cmake --build ${build}/${build_type} --config ${build_type}
    - cmake --install ${build}/${build_type} --prefix install
    - mv ${build}/${build_type} riscv64_build
    - rm -rf ${build}

    # 4. benchmark test
    - export CI=True
    - export KPU_TARGETS=k230
    - export NUC_PROXY_IP='**************'
    - export NUC_PROXY_PORT=10002
    - export BENCHMARK_NTT_REPORT_FILE=benchmark_ntt_report_`date "+%Y_%m_%d_%H_%M_%S"`.md
    - export BENCHMARK_NTT_MATMUL_X86_64_REPORT_FILE=benchmark_ntt_matmul_x86_64_report_`date "+%Y_%m_%d_%H_%M_%S"`.md
    - export BENCHMARK_NTT_MATMUL_RISCV64_REPORT_FILE=benchmark_ntt_matmul_riscv64_report_`date "+%Y_%m_%d_%H_%M_%S"`.md
    - python3 ntt/test/benchmark_test/benchmark_ntt.py --x86_64_path x86_64_build/bin/ --riscv64_target k230 --riscv64_path riscv64_build/bin/
    - echo -e "\n### 预置条件\n\n- x86_64 platform(AMD Ryzen Threadripper 3990X 64-Core Processor)\n\n- riscv64 platform(k230)\n\n- unit(cycle)\n\n- matmul dimensions:M = K = N = 32\n\n### benchmark\n\n $(cat benchmark_ntt_report*.md)" > benchmark_ntt_report*.md
    - echo -e "\n### 预置条件\n\n- x86_64 platform(AMD Ryzen Threadripper 3990X 64-Core Processor)\n\n### benchmark\n\n $(cat benchmark_ntt_matmul_x86_64_report_*.md)" > benchmark_ntt_matmul_x86_64_report_*.md
    - echo -e "\n### 预置条件\n\n- riscv64 platform(k230)\n\n### benchmark\n\n $(cat benchmark_ntt_matmul_riscv64_report_*.md)" > benchmark_ntt_matmul_riscv64_report_*.md
    - cp benchmark_ntt*.md ${K510_DIR}
    - confluence.md -l ${CF_HOST} -u ${CF_USERNAME} -t ${CF_TOKEN} --page_id ${CF_NTT_PAGE_ID} update --file benchmark_ntt_report*.md -v
    - confluence.md -l ${CF_HOST} -u ${CF_USERNAME} -t ${CF_TOKEN} --page_id ${CF_NTT_MATMUL_X86_64_PAGE_ID} update --file benchmark_ntt_matmul_x86_64_report_*.md -v
    - confluence.md -l ${CF_HOST} -u ${CF_USERNAME} -t ${CF_TOKEN} --page_id ${CF_NTT_MATMUL_RISCV64_PAGE_ID} update --file benchmark_ntt_matmul_riscv64_report_*.md -v

  artifacts:
    name: "k80_benchmark_ntt_test"
    when: always
    expire_in: never
    paths:
      - benchmark_ntt_report*.md
      - benchmark_ntt_matmul_x86_64_report_*.md
      - benchmark_ntt_matmul_riscv64_report_*.md

  timeout: 2h

"llm qemu test job":
  allow_failure: false
  rules:
    - if: '$CI_PIPELINE_SOURCE == "schedule" && $SCHEDULE_TYPE == "k80_llm_qemu"'
  tags:
    - nncase
  stage: test
  image:
    name: ${SDK_DOCKER_IMAGE}
    pull_policy: if-not-present
  script:
    - gitlab_runner_root=/compiler/gitlab-runner
    - k80=`pwd`
    - root=`pwd`/../
    - nncase=${root}/nncase
    - compiler_install=${root}/compiler_install
    - runtime_install=${root}/runtime_install
    - cd ..

    # 1. setup
    # 1.0 setup env
    - chmod 0400 ${PRIVATE_KEY}
    - mkdir -p ~/.ssh
    - touch ~/.ssh/known_hosts
    - ssh-keyscan g.a-bug.org >> ~/.ssh/known_hosts
    - apt update
    - cmake --version
    - apt remove --purge -y cmake cmake-data
    - rm -rf /usr/bin/cmake /usr/bin/ctest /usr/share/cmake-3.22*
    - cp -r ${gitlab_runner_root}/cmake/cmake-3.28.3-linux-x86_64 /opt
    - ln -s /opt/cmake-3.28.3-linux-x86_64/bin/* /usr/local/bin/
    - pip install conan==2.6.0 transformers==4.51.0 "confluence.md==0.2.3" onnx==1.17.0 GitPython wheel ml-dtypes
    - conan remote add sunnycase https://conan.sunnycase.moe --index 0 --force
    - export DOTNET_ROOT=${gitlab_runner_root}/dotnet/dotnet-sdk-8.0.111
    - export PATH=$PATH:${DOTNET_ROOT}
    - x86_64_gcc=${gitlab_runner_root}/toolchain/x86_64_gcc_14.2.0_ubuntu22.04
    - update-alternatives --install /usr/bin/gcc gcc ${x86_64_gcc}/bin/gcc-14.2.0 14
    - update-alternatives --install /usr/bin/g++ g++ ${x86_64_gcc}/bin/g++-14.2.0 14

    # 1.1 setup sdk
    - rm -rf duca duca.tgz
    - curl -# -L -o duca.tgz http://sw-file.a-bug.org/k80/common/sdk/duca/release/v0.3.2/duca-v0.3.2.tar.gz
    - mkdir -p duca
    - tar xzf duca.tgz -C duca
    - cd duca
    - ./setup.sh
    - cd -
    - duca=${root}/duca

    # 1.2 setup fllm
    - rm -rf fasterllm
    - ssh-agent bash -c "ssh-add ${PRIVATE_KEY};GIT_SSH_COMMAND='ssh -o StrictHostKeyChecking=no' <NAME_EMAIL>:software/k80/models/fasterllm.git"
    - cd fasterllm
    - git checkout feature/fallback_sync
    - paged_json="paged_attn_scheme.json"
    - paged_matmul_json="paged_attn_matmul_scheme.json"
    - apt install -y jq
    - |
      jq --arg pa_path $(pwd)/fasterllm/attention/distributed_flash_attention.h \
         '(.Outputs[0].CSourcePath) = $pa_path' \
         ${gitlab_runner_root}/llm_qemu_test/json/${paged_json} > ${paged_json}
    - |
      jq --arg pa_path $(pwd)/fasterllm/attention/distributed_flash_attention.h \
         --arg matmul_path $(pwd)/fasterllm/nncase_plugin.h \
         '.Outputs[0].CSourcePath = $pa_path | .Outputs[1].CSourcePath = $matmul_path' \
         ${gitlab_runner_root}/llm_qemu_test/json/${paged_matmul_json} > ${paged_matmul_json}
    - cd -

    # 1.3 setup nncase
    - rm -rf nncase
    - git clone https://github.com/kendryte/nncase.git -c http.proxy=$SOCKS_PROXY -c https.proxy=$SOCKS_PROXY
    - cd nncase
    - git reset --hard origin/${NNCASE_BRANCH}
    - git checkout ${NNCASE_COMMIT}
    - cd -
    - target=${duca}/output/qemu/images
    - |
      if [ ! -d model ]; then
          ln -s ${gitlab_runner_root}/llm_qemu_test/model model
      fi
    - |
      if [ ! -d data ]; then
          ln -s ${gitlab_runner_root}/llm_qemu_test/data data
      fi
    - |
      if [ ! -f ${target}/miniconda_env.sh ]; then
          cp data/miniconda_env.sh ${target}
      fi
    - |
      if [ ! -f ${target}/miniconda_env_test_huggingface_v0.1.tar.gz ]; then
          cp data/miniconda_env_test_huggingface_v0.1.tar.gz ${target}
      fi
    - |
      if [ ! -d script ]; then
          ln -s ${gitlab_runner_root}/llm_qemu_test/script script
      fi
    - |
      if [ ! -f ${target}/run_hf_model.py ]; then
          cp script/run_hf_model.py ${target}
      fi
    - |
      if [ ! -f ${target}/run_qemu_host.sh ]; then
          cp ${gitlab_runner_root}/llm_qemu_test/run_qemu_host.sh ${target}
      fi
    - cp ${gitlab_runner_root}/llm_qemu_test/login_qemu_host.sh .

    # 2. compiler build
    # 2.1 build nncase
    - cd ${nncase}
    - rm -rf CMakeUserPresets.json build
    - |
      conan install . --build=missing -s build_type=Release -pr:a=toolchains/x86_64-linux.profile.jinja \
                      -o "&:runtime=False" -o "&:python=True" -o "&:tests=False"
    - cmake --preset conan-release
    - cmake --build build/Release --config Release
    - cmake --install build/Release --prefix ${compiler_install}
    - cd -

    # 2.2 build k80
    - cd ${k80}
    - rm -rf CMakeUserPresets.json build
    - |
      conan install . --build=missing -s build_type=Release -pr:a=${nncase}/toolchains/x86_64-linux.profile.jinja \
                      -o "&:nncase_dir=${compiler_install}/lib/cmake/nncase" \
                      -o "&:runtime=False" -o "&:python=True" -o "&:tests=False"
    - cmake --preset conan-release
    - cmake --build build/Release --config Release
    - cmake --install build/Release --prefix=${compiler_install}
    - until dotnet restore; do :; done
    - dotnet build -c Release
    - cd -

    # 3. runtime build
    # 3.1 build nncase
    - cd ${nncase}
    - rm -rf build CMakeUserPresets.json runtime
    - |
      conan install . -of runtime --build=missing -s build_type=Release -pr:a=toolchains/x86_64-linux.profile.jinja \
                      -o "&:runtime=True" -o "&:python=True" -o "&:tests=False" -o "&:k80_runtime=True"
    - |
      cmake -DCMAKE_BUILD_TYPE=Release \
            -DBUILDING_RUNTIME=1 \
            -DENABLE_K80_RUNTIME=1 \
            -DENABLE_OPENMP=0 \
            -DBUILD_PYTHON_BINDING=1 \
            -DCMAKE_TOOLCHAIN_FILE=runtime/build/Release/generators/conan_toolchain.cmake \
            -DDEFAULT_BUILTIN_RUNTIMES=0 \
            -DENABLE_OP_PROFILE=0 \
            -DBUILD_BENCHMARK=0 \
            -S . \
            -B build
    - cmake --build build
    - cmake --install build --prefix=${runtime_install}
    - cd -

    # 3.2 build k80
    - cd ${k80}
    - rm -rf build CMakeUserPresets.json runtime
    - |
      conan install . -of runtime --build=missing -s build_type=Release -pr:a=../nncase/toolchains/x86_64-linux.profile.jinja \
                      -o "&:runtime=True" -o "&:python=True" -o "&:tests=False" -o "&:k80_runtime=True"
    - |
      cmake -DCMAKE_BUILD_TYPE=Release \
            -DBUILDING_RUNTIME=1 \
            -DENABLE_K80_RUNTIME=1 \
            -DENABLE_OPENMP=0 \
            -DCMAKE_TOOLCHAIN_FILE=runtime/build/Release/generators/conan_toolchain.cmake \
            -DDEFAULT_BUILTIN_RUNTIMES=0 \
            -DBUILD_PYTHON_BINDING=1 \
            -DENABLE_OP_PROFILE=0 \
            -DENABLE_XPU_RUNTIME=1 \
            -DDISABLE_RVV=0 \
            -Dnncaseruntime_DIR=${runtime_install}/lib/cmake/nncaseruntime/ \
            -DDUCA_PATH=${duca}/output/qemu/duca \
            -S . \
            -B build
    - cmake --build build
    - cmake --install build --prefix=${runtime_install}
    - export NNCASE_RUNTIME_LIB=${runtime_install}
    - cd python
    - python3 setup.py bdist_wheel
    - cp dist/nncaseruntime_xpu-*-py3-none-any.whl ${duca}/output/qemu/images/
    - cd ../../

    # 4. build LLM model
    - export LD_LIBRARY_PATH=${x86_64_gcc}/lib64:${compiler_install}/lib:${nncase}/src/Nncase.Cli/bin/Release/net8.0/runtimes/linux-x64/native:$LD_LIBRARY_PATH
    - export DYLD_LIBRARY_PATH=${compiler_install}/lib:$DYLD_LIBRARY_PATH
    - export PYTHONPATH=${PYTHONPATH}:${compiler_install}/lib:${compiler_install}/python:${nncase}/tests
    - export NNCASE_COMPILER=${nncase}/src/Nncase.Compiler/bin/Release/net8.0/Nncase.Compiler.dll
    - export NNCASE_PLUGIN_PATH=${k80}/modules/Nncase.Modules.XPU/bin/Release/net8.0
    - export PATH=${duca}/tools/llvm/bin/:$PATH
    - export DUCA_TOOLCHAIN_PATH=${duca}/tools/newlib/
    - export DUCA_XPUSDK_PATH=${duca}/output/qemu/xpusdk/
    - export SYS_MODE=1
    - export SOLVE_MAX_TIME=1800
    - artifact_dir=${gitlab_runner_root}/qemu_artifact/`date "+%Y_%m_%d_%H_%M_%S"`
    - mkdir -p ${artifact_dir}
    - llm_test_config="${gitlab_runner_root}/llm_qemu_test/json/llm_test_config.json"
    - cp ${llm_test_config} ${duca}/output/qemu/images
    - build_args=`jq -r 'map("\(.model) \(.num_hidden_layers) \(.seq_len)") | join(" ")' ${llm_test_config}`
    - echo "build_args=${build_args}"
    - bash ${gitlab_runner_root}/llm_qemu_test/build_model.sh ${build_args}
    - models=$(jq -r 'map(.model) | join(" ")' ${llm_test_config})
    - echo "models=$models"
    - |
      for model in $models; do
          cp tmp/$model/test.kmodel ${artifact_dir}/$model.kmodel
      done
    - rm -rf ${duca}/output/qemu/images/*.log

    # 5. run llm qemu test
    - id=$(whoami)
    - session="nncase_k80_llm_qemu_test"
    - tmux new-session -d -s ${session}
    - tmux split-window -v -t ${session}:0
    - tmux split-window -h -t ${session}:0.1
    - tmux ls

    # 5.1 start device
    - tmux select-window -t ${session}:0.1
    - tmux send-keys -t ${session}:0.1 'cd duca' C-m
    - sleep 1
    - tmux send-keys -t ${session}:0.1 "docker exec -it duca-${id} /bin/bash" C-m
    - sleep 1
    - tmux send-keys -t ${session}:0.1 './scripts/qemu-start-score.sh' C-m
    - sleep 10

    # 5.2 start host
    - tmux select-window -t ${session}:0.2
    - tmux send-keys -t ${session}:0.2 'cd duca' C-m
    - sleep 1
    - tmux send-keys -t ${session}:0.2 "docker exec -it duca-${id} /bin/bash" C-m
    - sleep 1
    - tmux send-keys -t ${session}:0.2 './scripts/qemu-start-x86_64.sh' C-m
    - sleep 10

    # 5.3 login host and run
    - tmux select-window -t ${session}:0.0
    - image_root=${duca}/output/qemu/images
    - done_file="${image_root}/${session}.txt"
    - |
      if [ -f $done_file ]; then
          rm ${done_file}
      fi
    - tmux send-keys -t ${session}:0.0 './login_qemu_host.sh' C-m
    - |
      while [ ! -f ${done_file} ]; do
          sleep 30
          for log in ${image_root}/*.log; do
              [ -f "${log}" ] && cp "${log}" ${artifact_dir}
          done
      done
    - tmux kill-session -t ${session}
    - json_file=${image_root}/llm_test_config.json
    - |
      for model in $models; do
          log_file=${image_root}/${model}*.log
          log=`cat ${log_file}|head -7`
          test_result="Pass"
          if grep -q "Matched Generation Tokens 3 / 3!" ${log_file}; then
              test_result="Pass"
          else
              test_result="Fail"
          fi

          jq --arg model "$model" \
             --arg result "$test_result" \
             --arg log "$log" \
             'map(if .model == $model then .test_result = $result | .log = $log else . end)' \
             ${json_file} > temp.json && mv temp.json ${json_file}
      done
    - md_file=${artifact_dir}/llm_qemu_config.md
    - python3 ${gitlab_runner_root}/llm_qemu_test/script/json2md.py --json ${json_file} --md ${md_file}
    - echo -e "\n### 预置条件\n\n- duca:$(cat duca/output/qemu/images/VERSION)\n\n### llm_qemu_test\n\n $(cat ${md_file})" > ${md_file}
    - confluence.md -l ${CF_HOST} -u ${CF_USERNAME} -t ${CF_TOKEN} --page_id ${CF_LLM_QEMU_TEST_PAGE_ID} update --file ${md_file} -v

  timeout: 16h