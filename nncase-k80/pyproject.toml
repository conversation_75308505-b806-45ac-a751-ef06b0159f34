[project]
name = "nncase-kpu"
dynamic = ["version"]
requires-python = ">=3.9"
authors = [{ name = "sunnycase" }, { email = "guo<PERSON>@canaan-creative.com" }]
maintainers = [{ name = "sunnycase" }, { email = "guo<PERSON>@canaan-creative.com" }]
readme = "README.md"
description = "kpu plug-in for nncase"
license = { file = "LICENSE" }
classifiers = [
    "Programming Language :: C++",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "License :: OSI Approved :: Apache Software License",
    "Operating System :: OS Independent",
]
keywords = ["kendryte", "nn", "compiler", "k510", "k230", "k80"]
dependencies = ["nncase"]

[project.urls]
Homepage = "https://g.a-bug.org/software/k80/nncase/nncase-k80"
documentation = "https://g.a-bug.org/software/k80/nncase/nncase-k80"
repository = "https://g.a-bug.org/software/k80/nncase/nncase-k80"

[build-system]
requires = ["setuptools>=42", "wheel==0.37.0", "conan==2.6.0", "ninja", "gitpython"]
build-backend = "setuptools.build_meta"

[tool.cibuildwheel]
build = ["cp39*", "cp310*", "cp311*", "cp312*", "cp313*"]
skip = "*musllinux*"
manylinux-x86_64-image = "sunnycase/manylinux_2_28_x86_64:1.0"
build-verbosity = 3

[tool.cibuildwheel.environment]
PYTHONPATH = "{project}/tests:$PYTHONPATH"

[tool.cibuildwheel.windows]
archs = ["AMD64"]
before-all = ["tools\\before-all.bat"]
before-build = [
    "rm -f {project}/CMakeUserPresets.json",
    "pip install auditwheel==6.0.0 wheeltools wheel==0.37 -i https://mirrors.aliyun.com/pypi/simple/"
]
repair-wheel-command = ["tools\\repair-wheel.bat {dest_dir} {wheel}"]

[tool.cibuildwheel.linux]
archs = ["x86_64"]
before-all = [
  "dnf install -y libuuid-devel",
  "pip install -U pip",
  "pip install conan==2.6.0",
  "conan remote add sunnycase https://conan.sunnycase.moe --index 0",
  "cd nncase",
  "rm -rf CMakeUserPresets.json",
  "conan install . --build=missing -s build_type=Release -pr:a=toolchains/x86_64-linux.profile.jinja -o \"&:runtime=False\" -o \"&:python=False\" -o \"&:tests=False\"",
  "cmake --preset conan-release",
  "cmake --build build/Release --config Release",
  "cmake --install build/Release --prefix=build"
]
before-build = [
  "rm -f {project}/CMakeUserPresets.json",
  "pip install https://github.com/sunnycase/auditwheel/releases/download/6.0.0/auditwheel-6.0.0-py3-none-any.whl wheel==0.37.0 wheeltools",
  "git config --global --add safe.directory {project}"
]
repair-wheel-command = [
  "LD_LIBRARY_PATH=/usr/lib64 auditwheel -v repair -w {dest_dir} {wheel} --exclude libvulkan.so.1 --exclude libgomp.so.1 --exclude libNncase.Runtime.Native.so",
  "python tools/convert_to_generic_platform_wheel.py -w {dest_dir} -r --py2-py3 {dest_dir}/`ls {dest_dir} -1 | head -n1`"
]

[tool.cibuildwheel.macos]
archs = ["arm64"]
before-build = [
  "rm -f {project}/CMakeUserPresets.json",
  "pip install auditwheel==6.0.0",
]

[tool.cibuildwheel.linux.environment]
CC = "gcc-14"
CXX = "g++-14"

[tool.cibuildwheel.macos.environment]
MACOSX_DEPLOYMENT_TARGET = "10.15"
